%cd /content/drive/My Drive/Google Colab/Computer Vision/EXP/People_Counting
%ls

import cv2
import os
import time
import sys
import numpy as np
from matplotlib import pyplot as plt

import dlib
from google.colab.patches import cv2_imshow

import imutils
from imutils.video import VideoStream
from imutils.video import FPS
from scipy.spatial import distance as dist
from collections import OrderedDict

#streamIP = "http://*************:8080/stream/input.mjpeg"

inputFile = "./inputs/test_3.mp4"
outputFile = "./outputs/output.avi"

scaleFactor = 1 / 255.0

# minimum probability of weak detections
minConfidence = 0.3

# threshold when applying non-maxima suppression
threshold = 0.2

elapsedFrames = 0

# switch between detection and tracking
# set number of frames to skip
skipFrames = 10

FPSUpdate = 200
liveFPS = 0

inputWidth = 416 
inputHeight = 416  

font = cv2.FONT_HERSHEY_SIMPLEX

class_file =  './utils/detect.names'
cfg_file = './utils/model.cfg'
weights_file = './utils/model.weights'

modelName = "Shahin VX"

status = "off"

writer = None

# vs = cv2.VideoCapture(streamIP)

vs = cv2.VideoCapture(inputFile)

prop = cv2.CAP_PROP_FRAME_COUNT
totalFrames = int(vs.get(prop))
print("--> Total frames in input video {} ".format(totalFrames))

H = int(vs.get(cv2.CAP_PROP_FRAME_HEIGHT))
W = int(vs.get(cv2.CAP_PROP_FRAME_WIDTH))

limitIn = int(H/2 + H/9)
limitOut = int(H/2 - H/9)

print(W, H)

# load the COCO class labels:
classNames = open(class_file).read().strip().split("\n")

# Load the serialized caffe model from disk:
print("--> Loading model ...")

net = cv2.dnn.readNetFromDarknet(cfg_file, weights_file)

print("--> Loading model ... done !")



#!pip install opencv-contrib-python==********* --force-reinstall

# Get the output layer names:
def getOutputLayers(net):
    layerNames = net.getLayerNames()
    layerNames = [layerNames[i[0] - 1]for i in net.getUnconnectedOutLayers()]
    return layerNames

# function to draw bounding box on the detected object
def drawBoundingBox(frame, box, centroid, color):
    (startX, startY, endX, endY) = box

    cv2.rectangle(frame, (int(startX), int(startY)), (int(
        endX), int(endY)), color, thickness=2)

# return coordinates of the center (centroid) of a bbox
def computeCentroid(box):
    (startX, startY, endX, endY) = box
    return np.array([startX + ((endX - startX)/2), startY + ((endY - startY)/2)])

class TrackableObject:
    def __init__(self, objectID, centroid, zone):
        self.objectID = objectID
        self.centroids = [centroid]
        self.zone = zone

        self.counted = False

class CentroidTracker:
    def __init__(self, maxDisappeared=30, maxDistance=30):

        self.nextObjectID = 0
        self.objects = OrderedDict()
        self.disappeared = OrderedDict()

 
        self.maxDisappeared = maxDisappeared

        self.maxDistance = maxDistance

    def register(self, centroid):
        self.objects[self.nextObjectID] = centroid
        self.disappeared[self.nextObjectID] = 0
        self.nextObjectID += 1

    def deregister(self, objectID):
        del self.objects[objectID]
        del self.disappeared[objectID]

    def update(self, rects):
        if len(rects) == 0:
            for objectID in list(self.disappeared.keys()):
                self.disappeared[objectID] += 1

                if self.disappeared[objectID] > self.maxDisappeared:
                    self.deregister(objectID)

            return self.objects

        # initialize an array of input centroids for the current frame
        inputCentroids = np.zeros((len(rects), 2), dtype="int")

        # loop over the bounding box rectangles
        for (i, (startX, startY, endX, endY)) in enumerate(rects):
            cX = int((startX + endX) / 2.0)
            cY = int((startY + endY) / 2.0)
            inputCentroids[i] = (cX, cY)

        if len(self.objects) == 0:
            for i in range(0, len(inputCentroids)):
                self.register(inputCentroids[i])

        else:
            # grab the set of object IDs and corresponding centroids
            objectIDs = list(self.objects.keys())
            objectCentroids = list(self.objects.values())

            D = dist.cdist(np.array(objectCentroids), inputCentroids)


            rows = D.min(axis=1).argsort()

            cols = D.argmin(axis=1)[rows]


            usedRows = set()
            usedCols = set()

 
            for (row, col) in zip(rows, cols):

                if row in usedRows or col in usedCols:
                    continue

                if D[row, col] > self.maxDistance:
                    continue

                objectID = objectIDs[row]
                self.objects[objectID] = inputCentroids[col]
                self.disappeared[objectID] = 0

                usedRows.add(row)
                usedCols.add(col)


            unusedRows = set(range(0, D.shape[0])).difference(usedRows)
            unusedCols = set(range(0, D.shape[1])).difference(usedCols)

            if D.shape[0] >= D.shape[1]:
                for row in unusedRows:

                    objectID = objectIDs[row]
                    self.disappeared[objectID] += 1

                    if self.disappeared[objectID] > self.maxDisappeared:
                        self.deregister(objectID)

            else:
                for col in unusedCols:
                    self.register(inputCentroids[col])

        # return the set of trackable objects
        return self.objects

# object detection using SSD
def detect(frame, layerOutputs):
    # loop over the detections
    for output in layerOutputs:
        for detection in output:
          
            scores = detection[5:]
            classId = np.argmax(scores)
            confidence = scores[classId]

            # Filter out weak predictions:
            if confidence > minConfidence:
                box = detection[0:4] * np.array([W, H, W, H])
                (centerX, centerY, width, height) = box.astype("int")

                x = int(centerX - (width / 2))
                y = int(centerY - (height / 2))

                boxes.append([x, y, int(width), int(height)])
                confidences.append(float(confidence))
                classIds.append(classId)


    indices = cv2.dnn.NMSBoxes(boxes, confidences, minConfidence, threshold)

    for i in indices:
        i = i[0]
        
        if classIds[i] == 0:
            # get coordinates of the bbox
            box = boxes[i]
            left = box[0]
            top = box[1]
            width = box[2]
            height = box[3]
            right = left + width
            bottom = top + height

            box = [left, top, right, bottom]
     
            tracker = dlib.correlation_tracker()
            rect = dlib.rectangle(int(left), int(
                top), int(right), int(bottom))

            tracker.start_track(frame, rect)

            trackers.append(tracker)

            rects.append(box)

            centroid = computeCentroid(box)
           
            drawBoundingBox(frame, box, centroid, color=(0, 0, 255))

            cv2.putText(frame, status, (0, 115), font,
                        0.5, (0, 255, 0), 1, cv2.LINE_AA)
            
# object tracking using dlib and centroid tracker
def track(frame, trackers):
    frame_num = 0
    for tracker in trackers:
        status = "Tracking ."
        tracker.update(frame)

        pos = tracker.get_position()

        # unpack the position object
        left = int(pos.left())
        top = int(pos.top())
        right = int(pos.right())
        bottom = int(pos.bottom())

        box = [left, top, right, bottom]

        rects.append(box)

        centroid = computeCentroid(box)

        drawBoundingBox(frame, box, centroid, color=(0, 128, 255))

        if frame_num % 2==0:
          status = "Tracking ..."

        cv2.putText(frame, status, (0, 95), font,
                    0.5, (255, 0, 0), 1, cv2.LINE_AA)
        frame_num += 1

# people counting logic based on zone of appearance
def counting(objects):
    
    global totalIn
    global totalOut
    
    # loop over the tracked objects
    for (objectID, centroid) in objects.items():

        to = trackableObjects.get(objectID, None)
        
        if to is None:
            if centroid[1] >= H/2:
                zone = "in"
            else :
                zone = "out"
                
            to = TrackableObject(objectID, centroid, zone)

        else:
            if to.zone == "in" :
                if centroid[1] < limitOut:
                    totalOut += 1
                    to.zone = "out"
                    print("OUT : ", totalOut)
                    
            elif to.zone == "out" :
                if centroid[1] > limitIn:
                    totalIn += 1
                    to.zone = "in"
                    print("IN  : ", totalIn)
            
            to.centroids.append(centroid)
                        
        trackableObjects[objectID] = to
        cv2.circle(frame, (centroid[0], centroid[1]), 4, (0, 0, 255), -1)
        cv2.putText(frame, "ID : " + str(objectID), (centroid[0], centroid[1]+20), font,
                    0.6, (0, 0, 255), 1, cv2.LINE_AA)

ct = CentroidTracker(maxDisappeared=30, maxDistance=130)

trackers = []
trackableObjects = {}

totalOut = 0
totalIn = 0

fps = FPS().start()
totalFPS = FPS().start()

# loop over frames from the video file stream
while True:
    # read the next frame from the file
    (grabbed, frame) = vs.read()

    if not grabbed:
        print("--> Video Traking Done !!!")
        break
    
    # list of detected rectangles
    rects = []

    if elapsedFrames % skipFrames == 0:
        classIds = []
        confidences = []
        boxes = []

        trackers = []
        status = "Detecting"

        blob = cv2.dnn.blobFromImage(
            frame, scaleFactor, (inputWidth, inputHeight), swapRB=True, crop=False)

        net.setInput(blob)

        start = time.time()
        layerOutputs = net.forward(getOutputLayers(net))
        end = time.time()

        detect(frame, layerOutputs)

    else:
        track(frame, trackers)

    objects = ct.update(rects)
    counting(objects)

    cv2.line(frame, (0, limitIn), (W, limitIn), (0, 255, 0), 1)
    cv2.line(frame, (0, limitOut), (W, limitOut), (255, 0, 0), 1)

    info = [
        ("Total  ", int(totalIn) + int(totalOut)),
        ("Get Out", totalOut),
        ("Get In ", totalIn)
    ]
    
    for (i, (k, v)) in enumerate(info):
        text = "{}: {}".format(k, v)
        cv2.putText(frame, text, (10, limitIn - ((i * 20) + 20)),
                    font, 0.6, (255, 255, 255), 1, cv2.LINE_AA)

    elapsedFrames += 1
    fps.update()

    if elapsedFrames % FPSUpdate == 0:
        fps.stop()
        liveFPS = fps.fps()
        print("--> Total Time Remaining (min.sec) : {:.1f}".format(
            (totalFrames-elapsedFrames) / int(liveFPS) / 60))

        # start the frames per second throughput estimator
        fps = FPS().start()
    
    # draw metadatas on the frame
    cv2.putText(frame, "Model : " + modelName, (0, limitOut-15),
                font, 0.5, (0, 255, 0), 1, cv2.LINE_AA)
    cv2.putText(frame, "Resolution : " + str(W) + "x" + str(H),
                (0, limitOut-35), font, 0.5, (0, 255, 0), 1, cv2.LINE_AA)
    cv2.putText(frame, "FPS: {:.1f}".format(liveFPS),
                (0, limitOut-55), font, 0.5, (0, 255, 0), 1, cv2.LINE_AA)
    cv2.putText(frame, "Detection : {:.2f} sec".format(
        end - start), (0, limitOut-75), font, 0.5, (0, 255, 0), 1, cv2.LINE_AA)

    totalFPS.update()
    
    # show the video beeing processed live
    #cv2.imshow('RPI', frame)
    #cv2.imshow(frame)

    # check if the video writer is None
    if writer is None:
        # initialize our video writer
        fourcc = cv2.VideoWriter_fourcc(*"MJPG")
        writer = cv2.VideoWriter(outputFile, fourcc, 30, (W, H), True)
        print("Video Loading ... ")

    # write the output frame to disk
    writer.write(frame)
    
    if cv2.waitKey(1) == ord('q'):
        break

totalFPS.stop()
print('\n############################# Final Result ############################\n')
print("\t\t --> Average FPS was : {:.2f}".format(totalFPS.fps()))
print("\t\t --> Total OUT : ", totalOut)
print("\t\t --> Total IN  : ", totalIn)
print("\t\t --> Total People Count : ", int(totalIn) + int(totalOut))
print('\n################################# END #################################')

# release the file pointers
writer.release()
vs.release()

# close any open windows
cv2.destroyAllWindows()

# define helper function to display videos
import io 
from IPython.display import HTML
from base64 import b64encode
def show_video(file_name, width=640):
  # show resulting counting video
  mp4 = open(file_name,'rb').read()
  data_url = "data:video/mp4;base64," + b64encode(mp4).decode()
  return HTML("""
  <video width="{0}" controls>
        <source src="{1}" type="video/mp4">
  </video>
  """.format(width, data_url))

# convert resulting video from avi to mp4 file format
import os
path_video = os.path.join("outputs","output.avi")
%cd outputs/
!ffmpeg -y -loglevel panic -i output.avi result.mp4
%cd ..

# output object tracking video
path_output = os.path.join("outputs","result.mp4")
show_video(path_output, width=960)

import io 
from IPython.display import HTML
from base64 import b64encode
def show_video(file_name, width=640):
  # show resulting counting video
  mp4 = open(file_name,'rb').read()
  data_url = "data:video/mp4;base64," + b64encode(mp4).decode()
  return HTML("""
  <video width="{0}" controls>
        <source src="{1}" type="video/mp4">
  </video>
  """.format(width, data_url))

show_video(path_output, width=960)

